// SPDX-License-Identifier: GPL-2.0+
/*
 * GC9307 SPI Display Driver for U-Boot
 * Based on Linux driver by liming
 * Ported for TI AM62X platform with U-Boot video subsystem
 * 
 * Copyright (C) 2025 Victel
 */

#include <common.h>
#include <command.h>
#include <cpu_func.h>
#include <dm.h>
#include <errno.h>
#include <spi.h>
#include <video.h>
#include <asm/gpio.h>
#include <asm-generic/gpio.h>
#include <dm/device_compat.h>
#include <linux/delay.h>

/* GC9307 Display Parameters */
#define GC9307_WIDTH    320
#define GC9307_HEIGHT   240
#define GC9307_BPP      16  /* 16 bits per pixel (RGB565) */

/* GC9307 Commands */
#define GC9307_SLPOUT   0x11  /* Sleep Out */
#define GC9307_DISPON   0x29  /* Display On */
#define GC9307_CASET    0x2A  /* Column Address Set */
#define GC9307_RASET    0x2B  /* Row Address Set */
#define GC9307_RAMWR    0x2C  /* Memory Write */
#define GC9307_MADCTL   0x36  /* Memory Access Control */
#define GC9307_COLMOD   0x3A  /* Interface Pixel Format */

/* Rotation modes */
enum gc9307_rotation {
    GC9307_ROTATE_0 = 0,
    GC9307_ROTATE_90,
    GC9307_ROTATE_180,
    GC9307_ROTATE_270
};

/* GC9307 private data structure */
struct gc9307_priv {
    struct gpio_desc reset_gpio;
    struct gpio_desc dc_gpio;
    struct gpio_desc cs_gpio;
    u32 rotation;
    u32 width;
    u32 height;
};

/* Forward declarations */
static int gc9307_spi_write_cmd(struct udevice *dev, u8 cmd);
static int gc9307_spi_write_data(struct udevice *dev, u8 data);
static int gc9307_spi_write_data_buf(struct udevice *dev, u8 *buf, u32 len);
static int gc9307_set_address_window(struct udevice *dev, u32 x1, u32 y1, u32 x2, u32 y2);
static int gc9307_init_display(struct udevice *dev);
static int gc9307_video_sync(struct udevice *dev);

/**
 * gc9307_spi_write_cmd - Write command to GC9307 via SPI
 * @dev: SPI device
 * @cmd: Command byte to send
 * 
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_spi_write_cmd(struct udevice *dev, u8 cmd)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC low for command */
    ret = dm_gpio_set_value(&priv->dc_gpio, 0);
    if (ret) {
        dev_dbg(dev, "Failed to set DC GPIO for command\n");
        return ret;
    }
    
    /* Send command via SPI */
    ret = dm_spi_xfer(dev, 8, &cmd, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        dev_dbg(dev, "Failed to send command 0x%02x\n", cmd);
        return ret;
    }
    
    return 0;
}

/**
 * gc9307_spi_write_data - Write single data byte to GC9307 via SPI
 * @dev: SPI device
 * @data: Data byte to send
 * 
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_spi_write_data(struct udevice *dev, u8 data)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret) {
        dev_dbg(dev, "Failed to set DC GPIO for data\n");
        return ret;
    }
    
    /* Send data via SPI */
    ret = dm_spi_xfer(dev, 8, &data, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        dev_dbg(dev, "Failed to send data 0x%02x\n", data);
        return ret;
    }
    
    return 0;
}

/**
 * gc9307_spi_write_data_buf - Write data buffer to GC9307 via SPI
 * @dev: SPI device
 * @buf: Data buffer to send
 * @len: Length of data buffer
 * 
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_spi_write_data_buf(struct udevice *dev, u8 *buf, u32 len)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret) {
        dev_dbg(dev, "Failed to set DC GPIO for data buffer\n");
        return ret;
    }
    
    /* Send data buffer via SPI */
    ret = dm_spi_xfer(dev, len * 8, buf, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        dev_dbg(dev, "Failed to send data buffer (len=%u)\n", len);
        return ret;
    }
    
    return 0;
}

void LCD_Clear(struct udevice *dev)
{
    u16 *memory;

    memory = (u16 *)kzalloc(GC9307_WIDTH * GC9307_HEIGHT * 2, GFP_KERNEL);

    gc9307_set_address_window(dev, 0, 0, GC9307_WIDTH - 1, GC9307_HEIGHT - 1);
    gc9307_spi_write_cmd(dev, GC9307_RAMWR);

    memset(memory,0xFF,GC9307_WIDTH * GC9307_HEIGHT * 2); //清黑屏
    printf("memory:%#X\n", memory[50]);

    gc9307_spi_write_data_buf(dev,(u8 *)memory, GC9307_WIDTH*GC9307_HEIGHT*2);

    kfree(memory);
}

/**
 * gc9307_set_address_window - Set display address window
 * @dev: SPI device
 * @x1: Start column
 * @y1: Start row  
 * @x2: End column
 * @y2: End row
 * 
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_set_address_window(struct udevice *dev, u32 x1, u32 y1, u32 x2, u32 y2)
{
    int ret;
    
    /* Set column address */
    ret = gc9307_spi_write_cmd(dev, GC9307_CASET);
    if (ret) return ret;
    
    ret = gc9307_spi_write_data(dev, x1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 & 0xFF);
    if (ret) return ret;
    
    /* Set row address */
    ret = gc9307_spi_write_cmd(dev, GC9307_RASET);
    if (ret) return ret;
    
    ret = gc9307_spi_write_data(dev, y1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 & 0xFF);
    if (ret) return ret;
    
    return 0;
}

/**
 * gc9307_init_display - Initialize GC9307 display controller
 * @dev: SPI device
 * 
 * This function contains the complete initialization sequence
 * ported from the Linux driver.
 * 
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_init_display(struct udevice *dev)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Hardware reset sequence */
    ret = dm_gpio_set_value(&priv->reset_gpio, 0);
    if (ret) return ret;
    mdelay(50);
    
    ret = dm_gpio_set_value(&priv->reset_gpio, 1);
    if (ret) return ret;
    mdelay(50);
    
    ret = dm_gpio_set_value(&priv->reset_gpio, 0);
    if (ret) return ret;
    mdelay(120);
    
    /* Sleep Out */
    ret = gc9307_spi_write_cmd(dev, 0x11);
    if (ret) return ret;
    mdelay(120);
    
    /* Initial commands */
    ret = gc9307_spi_write_cmd(dev, 0xfe);
    if (ret) return ret;
    ret = gc9307_spi_write_cmd(dev, 0xef);
    if (ret) return ret;
    
    /* Memory Access Control - set rotation */
    ret = gc9307_spi_write_cmd(dev, GC9307_MADCTL);
    if (ret) return ret;
    
    if (priv->rotation == GC9307_ROTATE_0) {
        ret = gc9307_spi_write_data(dev, 0xe8); /* 0 degree rotation */
    } else if (priv->rotation == GC9307_ROTATE_180) {
        ret = gc9307_spi_write_data(dev, 0x38); /* 180 degree rotation */
    } else {
        ret = gc9307_spi_write_data(dev, 0xe8); /* Default to 0 degree */
    }
    if (ret) return ret;
    
    /* Interface Pixel Format - RGB565 */
    ret = gc9307_spi_write_cmd(dev, GC9307_COLMOD);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x05);
    if (ret) return ret;

    /* Power control settings */
    ret = gc9307_spi_write_cmd(dev, 0x86);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x98);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x89);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x03);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x80);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8e);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0f);
    if (ret) return ret;

    /* Frame rate control */
    ret = gc9307_spi_write_cmd(dev, 0xe8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x12);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* More power control settings */
    ret = gc9307_spi_write_cmd(dev, 0xc3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x47);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc4);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc9);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* Extended command set */
    ret = gc9307_spi_write_cmd(dev, 0xff);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x62);
    if (ret) return ret;

    /* Display enhancement */
    ret = gc9307_spi_write_cmd(dev, 0x99);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x3e);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x9d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4b);
    if (ret) return ret;

    /* Positive Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF0);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0c);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x31);
    if (ret) return ret;

    /* Negative Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF2);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x04);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x21);
    if (ret) return ret;

    /* Positive Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF1);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x78);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x76);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x33);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x2f);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0xaf);
    if (ret) return ret;

    /* Negative Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x38);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x74);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x72);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x6f);
    if (ret) return ret;

    /* Tearing Effect Line Off */
    ret = gc9307_spi_write_cmd(dev, 0x34);
    if (ret) return ret;

    /* Sleep Out (again) */
    ret = gc9307_spi_write_cmd(dev, GC9307_SLPOUT);
    if (ret) return ret;
    mdelay(120);

    /* Display ON */
    ret = gc9307_spi_write_cmd(dev, GC9307_DISPON);
    if (ret) return ret;

    /* Send black pixels to clear screen */
    // LCD_Clear(dev);

    printf("GC9307: Display initialization completed\n");
    return 0;
}

/**
 * gc9307_video_sync - Synchronize framebuffer to display
 * @dev: Video device
 *
 * This function transfers the framebuffer content to the GC9307 display
 * via SPI interface. Optimized for performance and reliability.
 *
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_video_sync(struct udevice *dev)
{
    struct video_priv *uc_priv = dev_get_uclass_priv(dev);
    struct gc9307_priv *priv = dev_get_priv(dev);
    // u8 *fb = (u8 *)uc_priv->fb;
    int ret;
    u32 expected_size;
	u8 data1, data2;
	u8 *start = uc_priv->fb;

	ret = dm_spi_claim_bus(dev);
	if (ret) {
		dev_err(dev, "Failed to claim SPI bus: %d\n", ret);
		return ret;
	}

	/* start position X,Y */
    ret = gc9307_set_address_window(dev, 0, 0, priv->width - 1, priv->height - 1);
    if (ret) {
        dev_err(dev, "Failed to set address window\n");
        goto release_bus;
    }
    /* Memory write command */
    ret = gc9307_spi_write_cmd(dev, GC9307_RAMWR);

	for (int i = 0; i < (uc_priv->xsize * uc_priv->ysize); i++) {
		data2 = *start++;
		data1 = *start++;
        ret = gc9307_spi_write_data(dev, data1);
        if (ret) goto release_bus;
        ret = gc9307_spi_write_data(dev, data2);
        if (ret) goto release_bus;
	}

release_bus:
	dm_spi_release_bus(dev);

    return 0;

#if 0
    if (!fb) {
        dev_err(dev, "Framebuffer is NULL\n");
        return -EINVAL;
    }

    /* Validate framebuffer size */
    expected_size = priv->width * priv->height * (GC9307_BPP / 8);
    if (uc_priv->fb_size != expected_size) {
        dev_warn(dev, "FB size mismatch: expected %u, got %u\n",
                 expected_size, uc_priv->fb_size);
    }

    /* Claim SPI bus */
    ret = dm_spi_claim_bus(dev);
    if (ret) {
        dev_err(dev, "Failed to claim SPI bus: %d\n", ret);
        return ret;
    }

    /* Set address window to full screen */
#if 0
    ret = gc9307_set_address_window(dev, 0, 0, priv->width - 1, priv->height - 1);
    if (ret) {
        dev_err(dev, "Failed to set address window\n");
        goto release_bus;
    }
#endif

    /* Start memory write */
    ret = gc9307_spi_write_cmd(dev, GC9307_RAMWR);
    if (ret) {
        dev_err(dev, "Failed to send RAMWR command\n");
        goto release_bus;
    }


    u8 black_pixel[2] = {0xFF, 0xFF}; /* Black in RGB565 */
    int i;
    for (i = 0; i < priv->width * priv->height; i++) {
        ret = gc9307_spi_write_data_buf(dev, black_pixel, 2);
        if (ret) return ret;
    }
#if 0
    /* Transfer framebuffer data in chunks for better performance */
    u32 chunk_size = 4096; /* 4KB chunks */
    u32 remaining = uc_priv->fb_size;
    u8 *data_ptr = fb;

    while (remaining > 0) {
        u32 transfer_size = (remaining > chunk_size) ? chunk_size : remaining;

        ret = gc9307_spi_write_data_buf(dev, data_ptr, transfer_size);
        if (ret) {
            dev_err(dev, "Failed to transfer framebuffer chunk\n");
            goto release_bus;
        }

        data_ptr += transfer_size;
        remaining -= transfer_size;
    }

    dev_dbg(dev, "Framebuffer sync completed (%u bytes)\n", uc_priv->fb_size);
#endif
release_bus:
    dm_spi_release_bus(dev);
    return ret;
#endif
}

/**
 * gc9307_probe - Probe function for GC9307 driver
 * @dev: Video device
 *
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_probe(struct udevice *dev)
{
    struct video_uc_plat *plat = dev_get_uclass_plat(dev);
    struct video_priv *uc_priv = dev_get_uclass_priv(dev);
    struct gc9307_priv *priv = dev_get_priv(dev);
    struct udevice *spi_dev;
    struct dm_spi_slave_plat *spi_plat;
    int ret;

    printf("GC9307: Starting probe...\n");

    /* Get SPI device */
    spi_dev = dev->parent;
    if (!spi_dev) {
        dev_err(dev, "No SPI parent device found\n");
        return -ENODEV;
    }

    printf("GC9307: SPI parent device: %s, SPI device: %s\n", spi_dev->name, dev->name);

    /* Configure SPI parameters for OMAP SPI controller */
    spi_plat = dev_get_parent_plat(dev);
    if (spi_plat) {
        /* Use lower frequency for better signal integrity */
        spi_plat->max_hz = 25000000;  /* 25MHz for OMAP SPI stability */
        spi_plat->mode = SPI_MODE_0;  /* CPOL=0, CPHA=0 */
        spi_plat->cs = 0;              /* Chip select 0 */
        printf("GC9307: SPI configured for OMAP - freq=%d, mode=%d\n", spi_plat->max_hz, spi_plat->mode);
    }

    /* Get GPIO pins from device tree */
    ret = gpio_request_by_name(dev, "reset-gpio", 0, &priv->reset_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get reset GPIO\n");
        return ret;
    }

    ret = gpio_request_by_name(dev, "dc-gpio", 0, &priv->dc_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get DC GPIO\n");
        return ret;
    }

    ret = gpio_request_by_name(dev, "cs-gpio", 0, &priv->cs_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_warn(dev, "CS GPIO not found, using SPI CS\n");
        /* CS GPIO is optional, SPI controller can handle CS */
    }

    /* Get rotation from device tree */
    priv->rotation = dev_read_u32_default(dev, "rotate", 0);
    if (priv->rotation == 180) {
        priv->rotation = GC9307_ROTATE_180;
    } else {
        priv->rotation = GC9307_ROTATE_0;
    }

    /* Set display dimensions */
    priv->width = GC9307_WIDTH;
    priv->height = GC9307_HEIGHT;

    /* Configure video parameters */
    uc_priv->bpix = VIDEO_BPP16;  /* 16 bits per pixel */
    uc_priv->xsize = priv->width;
    uc_priv->ysize = priv->height;
    uc_priv->rot = 0;

    /* Calculate framebuffer size */
    plat->size = priv->width * priv->height * (GC9307_BPP / 8);

    ret = dm_spi_claim_bus(dev);
    if (ret) {
        dev_err(dev, "Failed to claim SPI bus: %d\n", ret);
        return ret;
    }


    /* Initialize display */
    ret = gc9307_init_display(dev);
    if (ret) {
        dev_err(dev, "Failed to initialize display\n");
        return ret;
    }

    printf("GC9307: %dx%d display ready\n", priv->width, priv->height);

    dm_spi_release_bus(dev);
    return 0;
}

/**
 * gc9307_bind - Bind function for GC9307 driver
 * @dev: Video device
 *
 * Returns: 0 on success, negative error code on failure
 */
static int gc9307_bind(struct udevice *dev)
{
    struct video_uc_plat *plat = dev_get_uclass_plat(dev);

    /* Set framebuffer size */
    plat->size = GC9307_WIDTH * GC9307_HEIGHT * (GC9307_BPP / 8);

    return 0;
}

/* Video operations structure */
static const struct video_ops gc9307_ops = {
    .video_sync = gc9307_video_sync,
};

/* Device tree compatible strings */
static const struct udevice_id gc9307_ids[] = {
    { .compatible = "victel,gc9307-uboot" },
    { .compatible = "jianghehai,xhzy1713268za1" },
    { }
};

/* U-Boot driver structure */
U_BOOT_DRIVER(gc9307_video) = {
    .name = "gc9307_video",
    .id = UCLASS_VIDEO,
    .of_match = gc9307_ids,
    .ops = &gc9307_ops,
    .bind = gc9307_bind,
    .probe = gc9307_probe,
    .priv_auto = sizeof(struct gc9307_priv),
};

/* U-Boot command to test GC9307 driver */
static int do_gc9307_test(struct cmd_tbl *cmdtp, int flag, int argc, char *const argv[])
{
	struct udevice *dev;
	int ret;

	printf("GC9307: Testing driver compilation and device detection\n");

	/* Try to find the device */
	ret = uclass_get_device_by_name(UCLASS_VIDEO, "display@0", &dev);
	if (ret) {
		printf("GC9307: Device 'display@0' not found (ret=%d)\n", ret);
	} else {
		printf("GC9307: Found device: %s\n", dev->name);
	}
    ret = dm_spi_claim_bus(dev);
    if (ret) {
        dev_err(dev, "Failed to claim SPI bus: %d\n", ret);
        return ret;
    }

    while(1)
    {
        mdelay(1000);
        LCD_Clear(dev);
    }

    dm_spi_release_bus(dev);
	return 0;
}

U_BOOT_CMD(
	llc, 1, 1, do_gc9307_test,
	"Test GC9307 driver",
	"Test GC9307 driver compilation and device detection"
);
